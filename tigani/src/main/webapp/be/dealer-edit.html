{% extends "be/include/preline-base.html" %}

{% block extrahead %}

    {% set menu = 'DEALER_COLLECTION' %}    

    <title>{% if curDealer.id is not empty %}Modifica rivenditore{% else %}Nuovo rivenditore{% endif %}</title>

    <!-- Page Libs -->
    {% include "be/include/snippets/plugins/datatable.html" %}
    {% include "be/include/snippets/plugins/daterangepicker.html" %}
    {% include "be/include/snippets/plugins/filepond.html" %}
    {% include "be/include/snippets/plugins/toastify.html" %}
    {% include "be/include/snippets/plugins/validate.html" %}
    {% include "be/include/snippets/plugins/maxlength.html" %}

{% endblock %}

{% block content %}
<div class="p-2 sm:p-5 md:pt-5 space-y-5">
      <!-- Create New User Card Form -->
      <div class="max-w-xl mx-auto">
        <!-- Breadcrumb -->
        <ol class="lg:hidden py-3 flex items-center whitespace-nowrap">
          <li class="flex items-center">

            <a class="py-0.5 px-1.5 flex items-center gap-x-1 text-sm rounded-md text-gray-600 hover:bg-gray-100 focus:bg-gray-100 focus:outline-hidden dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800" href="index.html">
              Rivenditori
            </a>
            <svg class="shrink-0 overflow-visible size-4  text-gray-400 dark:text-neutral-600" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
              <path d="M6 13L10 3" stroke="currentColor" stroke-linecap="round"></path>
            </svg>
          </li>          
          <li class="ps-1.5 flex items-center truncate font-semibold text-gray-800 dark:text-neutral-200 text-sm truncate">
            <span class="truncate">
              {% if curDealer.id is not empty %}
                Modifica Rivenditore
              {% else %}
                Aggiungi Rivenditore
              {% endif %}
            </span>
          </li>
        </ol>
        <!-- End Breadcrumb -->

        <!-- Header -->
        <div class="my-5 flex gap-x-3">
          <svg class="shrink-0 size-10 text-gray-400 dark:text-neutral-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10" />
            <circle cx="12" cy="10" r="3" />
            <path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662" />
          </svg>

          <div class="grow">
            <h1 class="font-semibold text-xl text-gray-800 dark:text-neutral-200">
              {% if curDealer.id is not empty %}
                Modifica Rivenditore
              {% else %}
                Aggiungi Rivenditore
              {% endif %}
            </h1>

            <p class="text-sm text-gray-500 dark:text-neutral-500">
              {% if curDealer.id is not empty %}
                Modifica i dati dell'azienda rivenditrice
              {% else %}
                Crea un'azienda rivenditrice e gestisci in seguito i suoi utenti
              {% endif %}
            </p>
          </div>
        </div>
        <!-- End Header -->

        {% include "be/include/snippets/pojo/dealer-form.html" %}
        
      </div>
      <!-- End Create New User Card Form -->
    </div>
{% endblock %}
{% block pagescript %}

    <!-- Reload -->
    <script class="reload-script-on-load">
        addRoute('BE_DEALER_DATA', '{{ routes("BE_DEALER_DATA") }}');
        addRoute('BE_DEALER_OPERATE', '{{ routes("BE_DEALER_OPERATE") }}');
        addRoute('BE_DEALER_FORM', '{{ routes("BE_DEALER_FORM") }}');
        addRoute('BE_DEALER_SAVE', '{{ routes("BE_DEALER_SAVE") }}');
        addRoute('BE_DEALER_VIEW', '{{ routes("BE_DEALER_VIEW") }}');
        addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    </script>

    <!-- Page Scripts -->    
    <script src="{{ contextPath }}/js/pages/dealer-form.js?{{ buildNumber }}"></script>
    
    <script>
        $(document).ready(function () {
            DealerForm.init()
        });
    </script>
       
{% endblock %}
